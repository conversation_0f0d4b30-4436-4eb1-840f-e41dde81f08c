import { useScanDetails } from "@/hooks/useScans";
import { formatDate } from "@/lib/dateUtils";
import { CloudProviderName } from "@/stores/cloudProviderStore";
import ContentLoader from "@components/ContentLoader";
import Badge from "@components/ui/Badge";
import Button from "@components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Cloud,
  CloudIcon,
  Database,
  Filter,
  RefreshCw,
  Shield,
} from "lucide-react";
import React, { useCallback, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";

const ScanDetails: React.FC = () => {
  const { scanId } = useParams<{ scanId: string }>();
  const navigate = useNavigate();
  const numericScanId = scanId ? parseInt(scanId, 10) : null;
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch scan details with automatic polling for in-progress scans
  const {
    data: scanDetailsResponse,
    isLoading,
    isError,
    error,
    refetch,
    lastUpdated,
  } = useScanDetails(numericScanId);

  // Format the last updated timestamp
  const formattedLastUpdated = lastUpdated
    ? formatDate(new Date(lastUpdated), { timeOnly: true })
    : "Never";

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    try {
      setIsRefreshing(true);
      await refetch();
      toast.success("Scan details refreshed successfully");
    } catch {
      toast.error("Failed to refresh scan details");
    } finally {
      setIsRefreshing(false);
    }
  }, [refetch, isRefreshing]);

  const scanDetails = scanDetailsResponse?.data;

  // Handle loading state
  if (isLoading) {
    return <ContentLoader type="detail" message="Loading scan details..." />;
  }

  // Handle error state
  if (isError || !scanDetails) {
    return (
      <Card>
        <CardContent className="py-16 flex flex-col items-center justify-center text-center">
          <div className="p-4 rounded-full bg-error-500/10 mb-4">
            <Shield size={32} className="text-error-400" />
          </div>
          <h3 className="text-lg font-medium text-dark-300 mb-2">
            Error Loading Scan Details
          </h3>
          <p className="text-dark-500 text-sm max-w-md mb-6">
            {error instanceof Error
              ? error.message
              : "There was an error loading the scan details. Please try again later."}
          </p>
          <Button variant="primary" onClick={handleRefresh}>
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Get cloud provider icon
  const getProviderIcon = () => {
    const providerName = scanDetails.cloud_provider as CloudProviderName;
    switch (providerName) {
      case "AWS":
        return <CloudIcon size={16} className="text-accent-500" />;
      case "GCP":
        return <Database size={16} className="text-primary-500" />;
      case "Azure":
        return <Cloud size={16} className="text-secondary-500" />;
      default:
        return <CloudIcon size={16} className="text-accent-500" />;
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "success";
      case "running":
        return "warning";
      case "failed":
        return "error";
      default:
        return "neutral";
    }
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-xl sm:text-2xl font-bold text-dark-100">
            Scan #{scanDetails.id}
          </h1>
          <p className="text-dark-400 text-sm sm:text-base">
            {scanDetails.account_name} ({scanDetails.account_id})
          </p>
          <div className="text-xs text-dark-500 mt-1 flex items-center">
            <Clock size={12} className="mr-1" />
            Last updated: {formattedLastUpdated}
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            leftIcon={
              <RefreshCw
                size={16}
                className={isRefreshing ? "animate-spin" : ""}
              />
            }
            className="w-full sm:w-auto"
          >
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      {/* Scan Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Scan Summary</CardTitle>
          <CardDescription>
            Overview of the security scan and its findings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <div>
              <h3 className="text-dark-400 text-sm mb-1">Cloud Provider</h3>
              <div className="flex items-center gap-2">
                {getProviderIcon()}
                <span className="text-dark-200">
                  {scanDetails.cloud_provider}
                </span>
              </div>
            </div>

            <div>
              <h3 className="text-dark-400 text-sm mb-1">Started</h3>
              <div className="flex items-center gap-2">
                <Calendar size={14} className="text-dark-500" />
                <span className="text-dark-300">
                  {formatDate(scanDetails?.scan_start, { includeTime: true })}
                </span>
              </div>
            </div>

            <div>
              <h3 className="text-dark-400 text-sm mb-1">Completed</h3>
              <div className="flex items-center gap-2">
                <Clock size={14} className="text-dark-500" />
                <span className="text-dark-300">
                  {scanDetails.scan_end
                    ? formatDate(scanDetails?.scan_end, { includeTime: true })
                    : "In Progress"}
                </span>
              </div>
            </div>

            <div>
              <h3 className="text-dark-400 text-sm mb-1">Status</h3>
              <Badge
                variant={getStatusBadgeVariant(scanDetails.status)}
                size="sm"
              >
                {scanDetails.status === "completed"
                  ? "Completed"
                  : scanDetails.status === "running"
                    ? "Running"
                    : "Failed"}
              </Badge>
            </div>

            <div className="col-span-full">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border border-dark-700 rounded-lg p-4 bg-dark-850">
                <div className="flex items-center gap-3">
                  <div className="bg-dark-800 p-2 rounded-full">
                    <Filter size={18} className="text-dark-400" />
                  </div>
                  <div>
                    <h3 className="text-dark-300">Total Checks</h3>
                    <p className="text-xl font-semibold text-dark-100">
                      {scanDetails.findings_count}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="bg-success-500/20 p-2 rounded-full">
                    <CheckCircle size={18} className="text-success-500" />
                  </div>
                  <div>
                    <h3 className="text-dark-300">Passed</h3>
                    <p className="text-xl font-semibold text-success-500">
                      {scanDetails.passed_findings}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="bg-error-500/20 p-2 rounded-full">
                    <AlertCircle size={18} className="text-error-500" />
                  </div>
                  <div>
                    <h3 className="text-dark-300">Failed</h3>
                    <p className="text-xl font-semibold text-error-500">
                      {scanDetails.failed_findings}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Services */}
      <Card>
        <CardHeader>
          <CardTitle>Services</CardTitle>
          <CardDescription>
            Status and findings for each scanned service
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-dark-800">
                  <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                    Service
                  </th>
                  <th className="text-center py-3 px-4 text-dark-400 font-medium text-sm">
                    Status
                  </th>
                  <th className="text-center py-3 px-4 text-dark-400 font-medium text-sm">
                    Last Scanned At
                  </th>
                  <th className="text-center py-3 px-4 text-dark-400 font-medium text-sm">
                    Checks
                  </th>
                  <th className="text-center py-3 px-4 text-dark-400 font-medium text-sm">
                    Passed
                  </th>
                  <th className="text-center py-3 px-4 text-dark-400 font-medium text-sm">
                    Failed
                  </th>
                  <th className="text-left py-3 px-6 text-dark-400 font-medium text-sm">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {scanDetails.services.map((service) => (
                  <tr
                    key={service.id}
                    className="border-b border-dark-800/50 hover:bg-dark-900/30 transition-colors"
                  >
                    <td className="py-3 px-4">
                      <span className="text-dark-200 font-medium">
                        {service.service_name}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <Badge
                        variant={getStatusBadgeVariant(service.status)}
                        size="sm"
                      >
                        {service.status === "completed"
                          ? "Completed"
                          : service.status === "running"
                            ? "Running"
                            : service.status === "pending"
                              ? "Pending"
                              : "Failed"}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-center text-dark-300">
                      {service.last_scanned_at
                        ? formatDate(service?.last_scanned_at, {
                            includeTime: true,
                          })
                        : "N/A"}
                    </td>
                    <td className="py-3 px-4 text-center text-dark-300">
                      {service.findings_count}
                    </td>
                    <td className="py-3 px-4 text-center text-success-500">
                      {service.passed_findings}
                    </td>
                    <td className="py-3 px-4 text-center text-error-500">
                      {service.failed_findings}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          navigate(
                            `/dashboard/scans/${scanDetails.id}/findings?service=${service.service_name}`
                          )
                        }
                        disabled={service.status !== "completed"}
                      >
                        View Findings
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ScanDetails;
